apiVersion: postgres-operator.crunchydata.com/v1beta1
kind: PostgresCluster
metadata:
  name: postgres-cluster-qa
  namespace: postgres-qa
  labels:
    app: postgres-qa
    environment: qa
spec:
  # Usar a imagem padrão mais recente - o operator gerencia as versões
  postgresVersion: 15
  
  # Instance configuration - recursos otimizados para QA
  instances:
    - name: instance1
      replicas: 1
      resources:
        requests:
          cpu: "250m"
          memory: "512Mi"
        limits:
          cpu: "1000m"
          memory: "2Gi"
      dataVolumeClaimSpec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: 10Gi
        # Usar storageClass padrão se standard-rwo não existir
        storageClassName: standard
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              podAffinityTerm:
                topologyKey: kubernetes.io/hostname
                labelSelector:
                  matchLabels:
                    postgres-operator.crunchydata.com/cluster: postgres-cluster-qa
                    postgres-operator.crunchydata.com/instance-set: instance1

  # Backup configuration usando volume local
  backups:
    pgbackrest:
      repos:
        - name: repo1
          volume:
            volumeClaimSpec:
              accessModes:
                - "ReadWriteOnce"
              resources:
                requests:
                  storage: 5Gi
              storageClassName: standard
      # Configuração de retenção para ambiente de QA
      global:
        repo1-retention-full: "3"
        repo1-retention-full-type: "time"
        repo1-retention-diff: "7"
        repo1-retention-diff-type: "time"

  # User and database configuration
  users:
    - name: conversas-ai-user-qa
      databases:
        - conversas_ai_qa
      options: "CREATEDB CREATEROLE"
      password:
        type: AlphaNumeric

  # PostgreSQL configuration otimizada para QA
  patroni:
    dynamicConfiguration:
      postgresql:
        parameters:
          # Configurações de conexão
          max_connections: "100"
          
          # Configurações de memória (otimizada para 2Gi limit)
          shared_buffers: "256MB"        # ~12.5% da RAM
          effective_cache_size: "1536MB"  # ~75% da RAM
          work_mem: "4MB"               # Para operações de ordenação
          maintenance_work_mem: "64MB"   # Para operações de manutenção
          
          # Configurações de WAL
          wal_buffers: "16MB"
          min_wal_size: "256MB"
          max_wal_size: "1GB"
          checkpoint_completion_target: "0.9"
          
          # Configurações de performance
          random_page_cost: "1.1"              # Para SSD
          effective_io_concurrency: "200"      # Para SSD
          default_statistics_target: "100"
          
          # Configurações de workers (otimizada para recursos limitados)
          max_worker_processes: "4"
          max_parallel_workers: "2"
          max_parallel_workers_per_gather: "1"
          max_parallel_maintenance_workers: "1"
          
          # Configurações específicas para ambiente de desenvolvimento/QA
          log_statement: "mod"           # Log de modificações
          log_min_duration_statement: "1000"  # Log queries > 1s
          log_line_prefix: "[%t] %u@%d "
          
          # Timezone
          timezone: "America/Sao_Paulo"
          
          # Configurações de autovacuum (importante para QA)
          autovacuum: "on"
          autovacuum_max_workers: "2"
          autovacuum_naptime: "30s"

  # Monitoring - Simplificado para QA
  monitoring:
    pgmonitor:
      exporter:
        resources:
          requests:
            cpu: "50m"
            memory: "64Mi"
          limits:
            cpu: "100m"
            memory: "128Mi"
