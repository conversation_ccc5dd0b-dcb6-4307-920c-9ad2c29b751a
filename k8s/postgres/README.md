# 🐘 PostgreSQL Deployment - CrunchyData Operator

Este diretório contém os arquivos necessários para deploy do PostgreSQL usando o CrunchyData postgres-operator no Kubernetes.

## 📁 Arquivos

- `namespace.yaml` - Namespace do PostgreSQL
- `secrets.yaml` - Cred<PERSON><PERSON><PERSON> seguras (senhas base64)
- `postgres-cluster.yaml` - Configuração do cluster PostgreSQL
- `service.yaml` - Services para acesso interno

- `pvc.yaml` - Persistent Volume Claims
- `CREDENTIALS.md` - Credenciais em texto claro (NÃO commitar!)
- `README.md` - Esta documentação

## 🚀 Deploy

### 1. Instalar CrunchyData Postgres Operator

```bash
# Instalar o operator
kubectl apply --server-side -k https://github.com/CrunchyData/postgres-operator/config/default

# Aguardar operator ficar pronto
kubectl wait --for=condition=Available deployment/pgo -n postgres-operator --timeout=300s
```

### 2. Deploy dos Componentes

```bash
# Aplicar na ordem
kubectl apply -f namespace.yaml
kubectl apply -f secrets.yaml
kubectl apply -f pvc.yaml
kubectl apply -f service.yaml
kubectl apply -f postgres-cluster.yaml

```

### 3. Verificar Status

```bash
# Aguardar cluster ficar pronto
kubectl wait --for=condition=PostgresClusterInitialized postgrescluster/postgres-cluster -n postgres --timeout=600s

# Verificar status
kubectl get postgrescluster -n postgres
kubectl get pods -n postgres
```

## 🔐 Acesso

### Interno (Cluster)
```
Host: postgres-service.postgres.svc.cluster.local
Port: 5432
Database: conversas_ai
User: conversas_ai_user
```

### Externo (Túnel Cloudflare)
```
Host: pg.conversas.ai
Port: 5432
Proteção: Cloudflare Access (<EMAIL>)
Protocolo: TCP direto via túnel
```

### Obter Senha
```bash
kubectl get secret postgres-cluster-pguser-conversas-ai-user -n postgres -o jsonpath='{.data.password}' | base64 -d
```

## ⚙️ Configuração

### Recursos
- **CPU**: 500m request, 2000m limit
- **Memory**: 1Gi request, 4Gi limit  
- **Storage**: 20Gi data + 20Gi backup + 10Gi WAL

### PostgreSQL Settings
- max_connections: 200
- shared_buffers: 256MB
- effective_cache_size: 1GB
- Backup retention: 14 dias

## 🔧 Manutenção

### Backup Manual
```bash
kubectl exec -it postgres-cluster-instance1-xxxx -n postgres -- pg_dump conversas_ai > backup.sql
```

### Escalar Replicas
```bash
kubectl patch postgrescluster postgres-cluster -n postgres --type='merge' -p='{"spec":{"instances":[{"name":"instance1","replicas":2}]}}'
```

### Atualizar Senhas
```bash
# Gerar nova senha
openssl rand -base64 32

# Atualizar secret
kubectl patch secret postgres-secrets -n postgres -p='{"data":{"APP_PASSWORD":"NOVA_SENHA_BASE64"}}'

# Reiniciar cluster
kubectl rollout restart deployment/postgres-cluster-instance1 -n postgres
```

### Aumentar Storage (PVC)
```bash
# 1. Verificar se StorageClass suporta expansão
kubectl get storageclass standard-rwo -o yaml | grep allowVolumeExpansion

# 2. Aumentar PVC do cluster PostgreSQL (exemplo: de 20Gi para 50Gi)
kubectl patch pvc postgres-cluster-instance1-pgdata -n postgres -p='{"spec":{"resources":{"requests":{"storage":"50Gi"}}}}'

# 3. Aumentar PVC de backup (se necessário)
kubectl patch pvc postgres-cluster-repo1-pgbackrest -n postgres -p='{"spec":{"resources":{"requests":{"storage":"50Gi"}}}}'

# 4. Verificar status da expansão
kubectl get pvc -n postgres -w

# 5. Aguardar expansão completar (pode demorar alguns minutos)
kubectl describe pvc postgres-cluster-instance1-pgdata -n postgres

# 6. Verificar espaço disponível no pod
kubectl exec postgres-cluster-instance1-xxxx -n postgres -- df -h /pgdata
```

**⚠️ Importante sobre Expansão de PVC:**
- Só funciona se o StorageClass tiver `allowVolumeExpansion: true`
- Não é possível diminuir o tamanho, apenas aumentar
- A expansão pode demorar alguns minutos dependendo do tamanho
- O PostgreSQL detecta automaticamente o novo espaço disponível

## 🌐 Cloudflare Tunnel

### Configuração do Túnel
1. **Criar túnel no Cloudflare Dashboard**:
   - Nome: `postgres-tunnel`
   - Tipo: TCP

2. **Configurar DNS**:
   - Tipo: CNAME
   - Nome: `pg.conversas.ai`
   - Destino: `<tunnel-id>.cfargotunnel.com`

3. **Configurar túnel para PostgreSQL**:
   ```yaml
   # cloudflared config.yml
   tunnel: <tunnel-id>
   credentials-file: /path/to/credentials.json

   ingress:
     - hostname: pg.conversas.ai
       service: tcp://postgres-service.postgres.svc.cluster.local:5432
     - service: http_status:404
   ```

4. **Configurar Access Policy**:
   - Application: `pg.conversas.ai`
   - Policy: Allow
   - Email: `<EMAIL>`

### Teste de Conectividade
```bash
# Via psql (após autenticação Cloudflare)
psql "postgresql://conversas_ai_user:<EMAIL>:5432/conversas_ai"

# Via pgAdmin ou outras ferramentas
# Host: pg.conversas.ai
# Port: 5432
# Database: conversas_ai
# Username: conversas_ai_user
```

## 📊 Monitoramento

### Métricas Disponíveis
- PostgreSQL Exporter integrado
- Métricas expostas na porta 9187
- Compatible com Prometheus/Grafana

### Logs
```bash
# Logs do PostgreSQL
kubectl logs -f postgres-cluster-instance1-xxxx -n postgres

# Logs do operator
kubectl logs -f deployment/pgo -n postgres-operator
```

### Monitoramento de Storage
```bash
# Verificar uso de storage nos PVCs
kubectl get pvc -n postgres

# Verificar uso de disco dentro do pod
kubectl exec postgres-cluster-instance1-xxxx -n postgres -- df -h

# Verificar tamanho do banco de dados
kubectl exec postgres-cluster-instance1-xxxx -n postgres -- psql -U postgres -c "SELECT pg_size_pretty(pg_database_size('conversas_ai'));"

# Verificar tabelas maiores
kubectl exec postgres-cluster-instance1-xxxx -n postgres -- psql -U postgres -d conversas_ai -c "SELECT schemaname,tablename,pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size FROM pg_tables ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC LIMIT 10;"
```

## 🚨 Troubleshooting

### Cluster não inicia
```bash
# Verificar events
kubectl describe postgrescluster postgres-cluster -n postgres

# Verificar logs do operator
kubectl logs deployment/pgo -n postgres-operator
```

### Problemas de conectividade
```bash
# Testar service interno
kubectl run test-pod --rm -i --tty --image=postgres:15 -- bash
psql "postgresql://conversas_ai_user:<EMAIL>:5432/conversas_ai"
```

### Storage issues
```bash
# Verificar PVCs
kubectl get pvc -n postgres

# Verificar storage usage
kubectl exec postgres-cluster-instance1-xxxx -n postgres -- df -h
```

## ⚠️ Segurança

- ✅ Senhas com 256 bits de entropia
- ✅ Armazenamento em Kubernetes Secrets
- ✅ Cloudflare Zero Trust protection
- ✅ Network policies (se configuradas)
- ✅ RBAC restrito ao namespace

## 📞 Suporte

Em caso de problemas:
1. Verificar logs do operator e cluster
2. Consultar documentação CrunchyData
3. Verificar configuração Cloudflare Zero Trust
